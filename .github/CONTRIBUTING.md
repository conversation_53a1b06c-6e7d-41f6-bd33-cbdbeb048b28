# 协作指南

欢迎参与《协商共和国》项目的创作！这个项目探索了一个独特的政治科幻世界，需要严格遵循既定的创作规则以保持一致性。

## 开始之前

在开始任何创作工作之前，请**务必仔细阅读**以下文档：

1. **[GEMINI.md](../GEMINI.md)** - 核心创作指导手册和项目概要（最重要）
2. **[worldbuilding/协商共和国世界构建手册.md](../worldbuilding/协商共和国世界构建手册.md)** - 详细世界观设定
3. **[worldbuilding/术语词汇表.md](../worldbuilding/术语词汇表.md)** - 标准术语指南
4. **[resources/风格指南.md](../resources/风格指南.md)** - 写作风格要求

## 创作原则

### 语调与风格
- **"直面荒诞的严肃表达"**：角色以极度认真的态度处理看似荒谬的情况
- **中立性**：不明确批评或赞扬任何政治制度
- **展示，不告诉**：通过情节和对话展现制度运作，避免直接说教
- **内在逻辑一致性**：所有制度和角色行为都必须有其内在合理性

### 语言要求
- 大量使用"美式官腔"标准表达
- 技术中性词汇：优化、协调、提升、协商等
- 正向表达优先：用"挑战"替代"问题"
- 集体导向语言：多用"我们"少用"我"

## 协作流程

### 1. 创作前准备
- 确定要创作的章节或场景
- 查阅相关角色发展弧线
- 确认时间线和情节连续性
- 选择合适的提示模板

### 2. 创作过程
- 使用 `prompts/提示模板/` 中的模板
- 严格遵循角色性格设定
- 保持术语使用的一致性
- 包含必要的AI系统反馈

### 3. 质量检查
- 自我审查：对照GEMINI.md进行检查
- 语言一致性：确保使用标准术语
- 情节连贯性：与现有内容保持一致
- 角色行为：符合既定性格发展

## 文件组织

### 手稿文件命名
```
manuscript/第X部分_部分名称/第X章_章节标题.md
```

### 提示文件保存
```
prompts/当前章节提示/章节名_创作提示.md
```

完成后移至：
```
prompts/已完成提示/章节名_创作提示.md
```

## AI协作指导

如果使用AI工具（如Cursor、Claude等）进行创作：

1. 始终将 `GEMINI.md` 作为主要指导文档
2. 在每次会话开始时重申项目的核心原则
3. 定期检查输出是否偏离既定风格
4. 使用项目提供的提示模板

## 内容审核标准

### 必须包含的元素
- 符合时间线的情节发展
- 角色行为的内在逻辑
- 适当的AI系统反馈
- 标准化的政治语言

### 需要避免的内容
- 直接的政治批判
- 简单的善恶二元对立
- 过度夸张的戏剧冲突
- 脱离角色设定的言行

## 反馈与讨论

### 创作讨论
- 在Issues中提出创作相关问题
- 使用标签系统分类讨论内容
- 引用具体的世界构建文档

### 修改建议
- 基于GEMINI.md提出修改意见
- 提供具体的改进方案
- 保持建设性和尊重性

## 版权与授权

- 所有贡献内容将成为项目的一部分
- 贡献者将在项目中获得适当署名
- 项目遵循[LICENSE](../LICENSE)中的授权条款

## 联系方式

如有任何疑问，请：
- 在Issues中提出问题
- 参考现有文档寻找答案
- 确保问题具体明确

---

感谢您的参与！让我们一起创造这个独特的政治科幻世界。 