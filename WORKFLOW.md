# 《协商共和国》AI辅助创作工作流手册 (v1.0)

> **"为混乱赋予秩序，为协作定义规则，为伟大作品构建坚实基石。"**

---

## 1. 引言：为何需要工作流

本手册旨在为《协商共和国》项目的人类创作者与AI协作者之间，建立一套清晰、高效、高质量的协作规范。它回答了“我们如何做”的问题，是我们项目创作的“操作手册”。

遵循此工作流，能够确保：
- **一致性 (Consistency)**：所有产出都符合项目的核心设定与风格。
- **高效性 (Efficiency)**：减少沟通成本，明确每一步的任务。
- **可追溯性 (Traceability)**：所有重大变更都有记录，便于回顾和迭代。
- **高质量 (Quality)**：通过标准化的质量控制，确保作品达到预期水准。

**核心指导原则**:
- **`GEMINI.md`** 是我们的**创作宪法**，定义了“是什么”和“为什么”。
- **`WORKFLOW.md`** (本文件) 是我们的**操作手册**，定义了“如何做”。
- 其他所有文档（如 `STRUCTURE.md`, `style.md`）都是对这两者的支撑和细化。

---

## 2. 核心创作流程：从创意到成稿

我们采用一个三阶段、五步骤的创作流程，确保创意的系统性注入和内容的有序产出。

### **阶段一：战略层 - 顶层设计**

#### **步骤 1: 更新世界观 (`worldbuilding/`)**
- **触发条件**: 当出现影响世界基本规则、核心技术、政治构架的宏大新创意时。
- **操作**: 修改 `worldbuilding/core.md`, `glosory.md`, `organization.md` 等相关文件。
- **产出**: 更新后的世界观设定文档。

#### **步骤 2: 修改大纲 (`OUTLINE.md`)**
- **触发条件**: 世界观发生变化，或需要调整故事主线、核心情节、三幕结构时。
- **操作**: 修改 `OUTLINE.md`，反映新的故事走向和章节规划。
- **产出**: 更新后的故事大纲。

### **阶段二：战术层 - 角色与提示**

#### **步骤 3: 更新角色 (`worldbuilding/charactor.md`)**
- **触发条件**: 世界观或大纲的变化影响到核心人物的动机、行为或命运时。
- **操作**: 修改 `worldbuilding/charactor.md`，调整角色传记、内心冲突或与其他角色的关系。
- **产出**: 更新后的角色设定。

#### **步骤 4: 创建/更新AI提示 (`prompts/`)**
- **触发条件**: 即将开始新章节的创作，或已有章节因上游变化需要重写时。
- **操作**: 在 `prompts/当前章节提示/` 目录下，根据 `OUTLINE.md` 和 `charactor.md` 的最新内容，创建详细的写作提示。
- **产出**: 一个清晰、可执行的AI创作指令（`.md`文件）。

### **阶段三：执行层 - 手稿撰写**

#### **步骤 5: 撰写/修改手稿 (`manuscript/`)**
- **触发条件**: AI提示准备就绪。
- **操作**:
    1. AI根据 `prompts/` 中的提示生成初稿。
    2. 人类创作者对初稿进行审阅、修订、润色。
    3. 双方可能就特定段落进行多轮协作。
- **产出**: 完成的章节手稿。

---

## 3. 版本与更新机制

### **任务追踪: `ROADMAP.md`**
- 用于规划宏观的创作任务，例如“完成第一幕的写作”、“重构第二幕大纲”等。

### **变更日志: `CHANGELOG.md` (建议)**
- 用于记录项目级别的重大更新。例如：
  - `v2.0.0 - 2025-07-04`: 引入全新AI辅助创作工作流。
  - `v1.5.0 - 2025-XX-XX`: 完成第一幕全部章节初稿。

---

## 4. 质量控制

我们采用严格的质量控制流程来确保所有产出的高标准。

- **评审时机**: 在完成任何一个章节或重大文档修改后，都应启动质量控制流程。
- **评审标准**: 详细的、包含三级检查清单（L1, L2, L3）的质量标准，请参阅我们的 **[《质量控制手册》(resources/quality_control.md)]**。
- **核心目标**: 确保作品在**基础一致性**、**风格与语调**、**暗讽与主题深度**三个层面都达到卓越标准。

---
*本手册是动态的，将根据项目进展持续完善。*
